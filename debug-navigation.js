// Debug script to test navigation state logic
// This simulates the logic from useNavigationState.ts

const certificateTypePages = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

function simulateNavigationState(certificateType, currentStatus) {
  const pages = certificateTypePages[certificateType];
  const currentPageIndex = pages.findIndex(page => page === currentStatus);
  
  if (currentPageIndex === -1) {
    console.error(`Invalid status page: ${currentStatus} for certificate type: ${certificateType}`);
    return null;
  }

  // Current logic: All pages before the current page are considered visited
  const visitedPages = pages.slice(0, currentPageIndex);
  
  const navigationState = {
    visitedPages,
    highestPageReached: Math.max(0, currentPageIndex - 1),
    currentPage: currentStatus,
    certificateType
  };

  return navigationState;
}

function checkPageStates(navigationState, currentPageType) {
  const { certificateType, visitedPages } = navigationState;
  const pages = certificateTypePages[certificateType];
  
  console.log(`\nPage states for ${certificateType} certificate with status "${currentPageType}":`);
  console.log(`Visited pages: [${visitedPages.join(', ')}]`);
  console.log(`Current page: ${currentPageType}`);
  
  pages.forEach(pageType => {
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);
    
    // Current page is always accessible
    const isCurrentPage = pageType === currentPageType;
    
    // Completed pages (before current page) are accessible
    const isCompleted = pageIndex !== -1 && currentPageIndex !== -1 && pageIndex < currentPageIndex;
    
    // Previously visited pages are accessible
    const isVisited = visitedPages.includes(pageType) && !isCompleted && !isCurrentPage;
    
    // Page is accessible if it's current, completed, or visited
    const isAccessible = isCurrentPage || isCompleted || isVisited;
    
    console.log(`  ${pageType}: ${isCurrentPage ? 'CURRENT' : isCompleted ? 'COMPLETED' : isVisited ? 'VISITED' : 'NOT_ACCESSIBLE'} (accessible: ${isAccessible})`);
  });
}

// Test the problematic case
console.log("=== Testing NWG/V certificate with status 'zusammenfassung' ===");
const navState = simulateNavigationState('NWG/V', 'zusammenfassung');
if (navState) {
  checkPageStates(navState, 'zusammenfassung');
}

// Test other cases for comparison
console.log("\n=== Testing NWG/V certificate with status 'verbrauch' ===");
const navState2 = simulateNavigationState('NWG/V', 'verbrauch');
if (navState2) {
  checkPageStates(navState2, 'verbrauch');
}

console.log("\n=== Testing WG/B certificate with status 'zusammenfassung' ===");
const navState3 = simulateNavigationState('WG/B', 'zusammenfassung');
if (navState3) {
  checkPageStates(navState3, 'zusammenfassung');
}
