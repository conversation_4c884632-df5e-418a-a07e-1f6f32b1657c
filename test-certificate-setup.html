<!DOCTYPE html>
<html>
<head>
    <title>Certificate Setup Test</title>
</head>
<body>
    <h1>Certificate Setup Test</h1>
    <p>This page will set up the test certificate and redirect to the app.</p>
    
    <script>
        // Set the test certificate ID in localStorage
        const testCertificateId = 'e79c5aff-bbf9-4378-8974-30a584c9dafc';
        localStorage.setItem('activeCertificateId', testCertificateId);
        
        console.log('Set activeCertificateId to:', testCertificateId);
        
        // Redirect to the summary page after a short delay
        setTimeout(() => {
            window.location.href = 'http://localhost:5174/erfassen/zusammenfassung';
        }, 1000);
    </script>
    
    <p>Setting up certificate ID: <code>e79c5aff-bbf9-4378-8974-30a584c9dafc</code></p>
    <p>Redirecting to summary page...</p>
</body>
</html>
