import { useState, useEffect, useCallback } from 'react';
import { useCertificate } from '../contexts/CertificateContext';
import { supabase } from '../lib/supabase';

// Define page types
export type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define certificate types
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Navigation state interface - now derived from database status
interface NavigationState {
  visitedPages: PageType[];
  highestPageReached: number;
  certificateId: string;
  currentPage: PageType;
  timestamp: number;
}

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

/**
 * Custom hook to manage navigation state for certificate editing flow
 * Now uses database-backed status tracking instead of session storage
 */
export const useNavigationState = (certificateType: CertificateType | null) => {
  const { activeCertificateId } = useCertificate();
  const [navigationState, setNavigationState] = useState<NavigationState | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load navigation state from database status
  const loadNavigationStateFromDatabase = useCallback(async (certId: string, certType: CertificateType): Promise<NavigationState | null> => {
    try {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('status')
        .eq('id', certId)
        .single();

      if (error) {
        console.error('Error loading certificate status:', error);
        return null;
      }

      if (!data || !data.status) {
        return null;
      }

      const currentPage = data.status as PageType;
      const pages = certificateTypePages[certType];
      const currentPageIndex = pages.findIndex(page => page === currentPage);

      if (currentPageIndex === -1) {
        console.warn(`Invalid status page: ${currentPage} for certificate type: ${certType}`);
        return null;
      }

      // All pages before the current page are considered visited
      const visitedPages = pages.slice(0, currentPageIndex);

      return {
        visitedPages,
        highestPageReached: Math.max(0, currentPageIndex - 1),
        certificateId: certId,
        currentPage,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Error loading navigation state from database:', error);
      return null;
    }
  }, []);

  // Update certificate status in database
  const updateCertificateStatus = useCallback(async (certId: string, pageType: PageType) => {
    try {
      const { error } = await supabase
        .from('energieausweise')
        .update({
          status: pageType,
          updated_at: new Date().toISOString()
        })
        .eq('id', certId);

      if (error) {
        console.error('Error updating certificate status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating certificate status:', error);
      return false;
    }
  }, []);

  // Initialize navigation state when certificate changes
  useEffect(() => {
    const initializeNavigationState = async () => {
      if (!activeCertificateId || !certificateType) {
        setNavigationState(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      const state = await loadNavigationStateFromDatabase(activeCertificateId, certificateType);

      if (state) {
        setNavigationState(state);
      } else {
        // Create new navigation state with default status 'objektdaten'
        const newState: NavigationState = {
          visitedPages: [],
          highestPageReached: -1,
          certificateId: activeCertificateId,
          currentPage: 'objektdaten',
          timestamp: Date.now()
        };
        setNavigationState(newState);

        // Set initial status in database
        await updateCertificateStatus(activeCertificateId, 'objektdaten');
      }

      setIsLoading(false);
    };

    initializeNavigationState();
  }, [activeCertificateId, certificateType, loadNavigationStateFromDatabase, updateCertificateStatus]);

  // Mark a page as visited by updating database status
  const markPageAsVisited = useCallback(async (pageType: PageType) => {
    // Early return if required dependencies are not available
    if (!certificateType || !activeCertificateId) return;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);

    if (pageIndex === -1) return;

    // Update database status
    const success = await updateCertificateStatus(activeCertificateId, pageType);

    if (success) {
      // Update local state to reflect the change
      setNavigationState(prevState => {
        if (!prevState) return prevState;

        // All pages before the current page are considered visited
        const visitedPages = pages.slice(0, pageIndex);

        return {
          ...prevState,
          visitedPages,
          highestPageReached: Math.max(0, pageIndex - 1),
          currentPage: pageType,
          timestamp: Date.now()
        };
      });
    }
  }, [certificateType, activeCertificateId, updateCertificateStatus]);

  // Check if a page is accessible (completed, visited, or current)
  const isPageAccessible = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    if (pageIndex === -1 || currentPageIndex === -1) return false;

    // Current page is always accessible
    if (pageType === currentPageType) return true;

    // Completed pages (before current page) are accessible
    if (pageIndex < currentPageIndex) return true;

    // Previously visited pages are accessible
    if (navigationState.visitedPages.includes(pageType)) return true;

    return false;
  }, [navigationState, certificateType]);

  // Check if a page is completed (before current page)
  const isPageCompleted = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    return pageIndex !== -1 && currentPageIndex !== -1 && pageIndex < currentPageIndex;
  }, [certificateType]);

  // Check if a page is visited but not completed
  const isPageVisited = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const isCompleted = isPageCompleted(pageType, currentPageType);
    const isVisited = navigationState.visitedPages.includes(pageType);

    return isVisited && !isCompleted && pageType !== currentPageType;
  }, [navigationState, certificateType, isPageCompleted]);

  // Clear navigation state (reset certificate to first page)
  const clearNavigationState = useCallback(async () => {
    if (activeCertificateId) {
      await updateCertificateStatus(activeCertificateId, 'objektdaten');
      setNavigationState(null);
    }
  }, [activeCertificateId, updateCertificateStatus]);

  return {
    navigationState,
    markPageAsVisited,
    isPageAccessible,
    isPageCompleted,
    isPageVisited,
    clearNavigationState,
    isLoading
  };
};
