/**
 * Test utility to verify the new page-based status tracking system
 * This file can be used to test navigation state reconstruction logic
 */

import { supabase } from '../lib/supabase';

// Define page types and certificate types (matching the hook)
export type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define the page configuration for each certificate type (matching the hook)
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

interface NavigationState {
  visitedPages: PageType[];
  highestPageReached: number;
  certificateId: string;
  currentPage: PageType;
  timestamp: number;
}

/**
 * Test function to reconstruct navigation state from database status
 */
export async function testNavigationStateReconstruction(certificateId: string): Promise<NavigationState | null> {
  try {
    // Get certificate data
    const { data, error } = await supabase
      .from('energieausweise')
      .select('status, certificate_type')
      .eq('id', certificateId)
      .single();

    if (error) {
      console.error('Error loading certificate:', error);
      return null;
    }

    if (!data || !data.status || !data.certificate_type) {
      console.error('Missing certificate data');
      return null;
    }

    const currentPage = data.status as PageType;
    const certificateType = data.certificate_type as CertificateType;
    const pages = certificateTypePages[certificateType];
    const currentPageIndex = pages.findIndex(page => page === currentPage);

    if (currentPageIndex === -1) {
      console.error(`Invalid status page: ${currentPage} for certificate type: ${certificateType}`);
      return null;
    }

    // All pages before the current page are considered visited
    const visitedPages = pages.slice(0, currentPageIndex);

    const navigationState: NavigationState = {
      visitedPages,
      highestPageReached: Math.max(0, currentPageIndex - 1),
      certificateId,
      currentPage,
      timestamp: Date.now()
    };

    console.log('Navigation state reconstructed:', {
      certificateId,
      certificateType,
      currentPage,
      visitedPages,
      allPages: pages
    });

    return navigationState;
  } catch (error) {
    console.error('Error reconstructing navigation state:', error);
    return null;
  }
}

/**
 * Test function to verify page accessibility logic
 */
export function testPageAccessibility(navigationState: NavigationState, certificateType: CertificateType, targetPage: PageType, currentPage: PageType): boolean {
  const pages = certificateTypePages[certificateType];
  const pageIndex = pages.findIndex(page => page === targetPage);
  const currentPageIndex = pages.findIndex(page => page === currentPage);

  if (pageIndex === -1 || currentPageIndex === -1) return false;

  // Current page is always accessible
  if (targetPage === currentPage) return true;

  // Completed pages (before current page) are accessible
  if (pageIndex < currentPageIndex) return true;

  // Previously visited pages are accessible
  if (navigationState.visitedPages.includes(targetPage)) return true;

  return false;
}

/**
 * Test all certificate types and status combinations
 */
export async function runNavigationStateTests() {
  console.log('🧪 Running navigation state tests...');

  // Test cases for each certificate type
  const testCases = [
    { type: 'WG/V' as CertificateType, status: 'objektdaten' as PageType, expectedVisited: [] },
    { type: 'WG/V' as CertificateType, status: 'gebaeudedetails1' as PageType, expectedVisited: ['objektdaten'] },
    { type: 'WG/V' as CertificateType, status: 'verbrauch' as PageType, expectedVisited: ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2'] },
    { type: 'WG/V' as CertificateType, status: 'zusammenfassung' as PageType, expectedVisited: ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'verbrauch'] },
    
    { type: 'WG/B' as CertificateType, status: 'objektdaten' as PageType, expectedVisited: [] },
    { type: 'WG/B' as CertificateType, status: 'fenster' as PageType, expectedVisited: ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2'] },
    { type: 'WG/B' as CertificateType, status: 'zusammenfassung' as PageType, expectedVisited: ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'fenster', 'heizung', 'tww-lueftung'] },
    
    { type: 'NWG/V' as CertificateType, status: 'verbrauch' as PageType, expectedVisited: ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2'] },
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    const pages = certificateTypePages[testCase.type];
    const currentPageIndex = pages.findIndex(page => page === testCase.status);
    const visitedPages = pages.slice(0, currentPageIndex);
    
    const passed = JSON.stringify(visitedPages) === JSON.stringify(testCase.expectedVisited);
    
    console.log(`${passed ? '✅' : '❌'} ${testCase.type} - ${testCase.status}:`, {
      expected: testCase.expectedVisited,
      actual: visitedPages,
      passed
    });
    
    if (passed) passedTests++;
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  return passedTests === totalTests;
}
