import React from 'react';
import { useCertificateType } from '../../hooks/useCertificateType';
import { useNavigationState } from '../../hooks/useNavigationState';
import { useCertificate } from '../../contexts/CertificateContext';

/**
 * Debug component to help diagnose navigation state issues
 * This component shows the current state of navigation hooks
 */
export const NavigationDebug: React.FC = () => {
  const { activeCertificateId } = useCertificate();
  const { certificateType, isLoading: certificateTypeLoading } = useCertificateType();
  const { 
    navigationState, 
    isPageAccessible, 
    isPageCompleted, 
    isPageVisited,
    isLoading: navigationLoading 
  } = useNavigationState(certificateType);

  const pages = certificateType ? {
    'WG/V': ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'verbrauch', 'zusammenfassung'],
    'WG/B': ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'fenster', 'heizung', 'tww-lueftung', 'zusammenfassung'],
    'NWG/V': ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'verbrauch', 'zusammenfassung']
  }[certificateType] : [];

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="font-bold text-lg mb-2">Navigation Debug</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Active Certificate ID:</strong> {activeCertificateId || 'None'}
        </div>
        
        <div>
          <strong>Certificate Type:</strong> {certificateType || 'Loading...'}
          {certificateTypeLoading && <span className="text-blue-500"> (Loading)</span>}
        </div>
        
        <div>
          <strong>Navigation Loading:</strong> {navigationLoading ? 'Yes' : 'No'}
        </div>
        
        {navigationState && (
          <>
            <div>
              <strong>Current Page:</strong> {navigationState.currentPage}
            </div>
            
            <div>
              <strong>Visited Pages:</strong> [{navigationState.visitedPages.join(', ')}]
            </div>
            
            <div>
              <strong>Highest Page Reached:</strong> {navigationState.highestPageReached}
            </div>
          </>
        )}
        
        {!navigationState && !navigationLoading && (
          <div className="text-red-500">
            <strong>Navigation State:</strong> Not loaded
          </div>
        )}
        
        {pages.length > 0 && navigationState && (
          <div>
            <strong>Page States:</strong>
            <ul className="mt-1 space-y-1">
              {pages.map(page => {
                const isCurrentPage = page === navigationState.currentPage;
                const isCompleted = isPageCompleted(page, navigationState.currentPage);
                const isVisited = isPageVisited(page, navigationState.currentPage);
                const isAccessible = isPageAccessible(page, navigationState.currentPage);
                
                let status = 'NOT_ACCESSIBLE';
                if (isCurrentPage) status = 'CURRENT';
                else if (isCompleted) status = 'COMPLETED';
                else if (isVisited) status = 'VISITED';
                
                return (
                  <li key={page} className={`text-xs ${
                    isCurrentPage ? 'text-blue-600 font-medium' :
                    isCompleted ? 'text-green-600' :
                    isVisited ? 'text-orange-600' :
                    'text-gray-400'
                  }`}>
                    {page}: {status} {isAccessible ? '✓' : '✗'}
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};
