// Test script to verify navigation state with real database data
// This simulates the exact logic from useNavigationState.ts with real data

const certificateTypePages = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

// Simulate the loadNavigationStateFromDatabase function
function simulateLoadNavigationState(certificateType, status) {
  console.log(`\n=== Simulating loadNavigationStateFromDatabase ===`);
  console.log(`Certificate Type: ${certificateType}`);
  console.log(`Status: ${status}`);

  const currentPage = status;
  const pages = certificateTypePages[certificateType];
  const currentPageIndex = pages.findIndex(page => page === currentPage);

  if (currentPageIndex === -1) {
    console.warn(`Invalid status page: ${currentPage} for certificate type: ${certificateType}`);
    return null;
  }

  // All pages before the current page are considered visited
  const visitedPages = pages.slice(0, currentPageIndex);

  const navigationState = {
    visitedPages,
    highestPageReached: Math.max(0, currentPageIndex - 1),
    certificateId: '9842a65c-adfb-444d-b12b-06c10c6f3772',
    currentPage,
    timestamp: Date.now()
  };

  console.log(`Current Page Index: ${currentPageIndex}`);
  console.log(`Visited Pages: [${visitedPages.join(', ')}]`);
  console.log(`Highest Page Reached: ${navigationState.highestPageReached}`);

  return navigationState;
}

// Simulate the breadcrumb logic
function simulateBreadcrumbLogic(navigationState, currentPageType, certificateType) {
  console.log(`\n=== Simulating Breadcrumb Logic ===`);
  console.log(`Current Page Type: ${currentPageType}`);

  const pages = certificateTypePages[certificateType];

  pages.forEach(pageType => {
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    // Current page is always accessible
    const isCurrentPage = pageType === currentPageType;

    // Completed pages (before current page) are accessible
    const isCompleted = pageIndex !== -1 && currentPageIndex !== -1 && pageIndex < currentPageIndex;

    // Previously visited pages are accessible
    const isVisited = navigationState.visitedPages.includes(pageType) && !isCompleted && !isCurrentPage;

    // Page is accessible if it's current, completed, or visited
    const isAccessible = isCurrentPage || isCompleted || isVisited;

    let status = 'NOT_ACCESSIBLE';
    if (isCurrentPage) status = 'CURRENT';
    else if (isCompleted) status = 'COMPLETED';
    else if (isVisited) status = 'VISITED';

    console.log(`  ${pageType}: ${status} (accessible: ${isAccessible})`);
  });
}

// Test with the real certificate data
console.log('=== Testing WG/V Certificate ===');
let certificateType = 'WG/V';
let status = 'zusammenfassung';

let navState = simulateLoadNavigationState(certificateType, status);
if (navState) {
  simulateBreadcrumbLogic(navState, status, certificateType);
}

console.log('\n=== Testing WG/B Certificate ===');
certificateType = 'WG/B';
status = 'zusammenfassung';

navState = simulateLoadNavigationState(certificateType, status);
if (navState) {
  simulateBreadcrumbLogic(navState, status, certificateType);
}